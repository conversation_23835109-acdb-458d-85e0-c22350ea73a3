import React, { useState } from 'react';
import { Search, MapPin, Users, Home, Star, ChevronDown } from 'lucide-react';

// Property type options
interface PropertyType {
  id: string;
  label: string;
  icon: React.ReactElement;
  description: string;
}

const propertyTypes: PropertyType[] = [
  {
    id: 'putra',
    label: 'Kost Putra',
    icon: <Users className="w-5 h-5" />,
    description: 'Khusus Laki-laki'
  },
  {
    id: 'putri',
    label: 'Kost Putri',
    icon: <Users className="w-5 h-5" />,
    description: 'Khusus Perempuan'
  },
  {
    id: 'campur',
    label: 'Kost Campur',
    icon: <Users className="w-5 h-5" />,
    description: '<PERSON><PERSON>-laki & Perempuan'
  }
];

// Hero section props interface
interface HeroSectionProps {
  onSearch?: (query: string, propertyType: string) => void;
}

const HeroSection: React.FC<HeroSectionProps> = ({ onSearch }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedPropertyType, setSelectedPropertyType] = useState('');
  const [isPropertyTypeOpen, setIsPropertyTypeOpen] = useState(false);

  // Colors for light theme only
  const colors = {
    sectionBg: 'linear-gradient(135deg, #27374D 0%, #526D82 50%, #9DB2BF 100%)',
    primaryText: '#DDE6ED',
    secondaryText: 'rgba(221, 230, 237, 0.9)',
    cardBg: 'rgba(221, 230, 237, 0.8)',
    inputBg: 'rgba(221, 230, 237, 0.7)',
    inputBorder: '#9DB2BF',
    inputText: '#27374D',
    buttonBg: '#DDE6ED',
    buttonText: '#27374D',
    buttonHover: 'rgba(221, 230, 237, 0.9)',
    statsBg: 'rgba(221, 230, 237, 0.6)',
    statsText: '#27374D',
    blobColor1: 'rgba(157, 178, 191, 0.2)',
    blobColor2: 'rgba(221, 230, 237, 0.1)',
    blobColor3: 'rgba(82, 109, 130, 0.2)',
    dotColor: 'rgba(221, 230, 237, 0.6)'
  };



  // Handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchQuery, selectedPropertyType);
    }
  };

  // Handle property type selection
  const handlePropertyTypeSelect = (type: PropertyType) => {
    setSelectedPropertyType(type.id);
    setIsPropertyTypeOpen(false);
  };

  // Get selected property type label
  const getSelectedPropertyTypeLabel = () => {
    const selected = propertyTypes.find(type => type.id === selectedPropertyType);
    return selected ? selected.label : 'Pilih Tipe Kost';
  };

  return (
    <section className="relative min-h-screen overflow-hidden">
      {/* Dynamic gradient background based on theme */}
      <div className="absolute inset-0" style={{ background: colors.sectionBg }}>
        {/* Animated gradient mesh using dynamic colors */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute top-0 -left-4 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl animate-blob" style={{ backgroundColor: colors.blobColor1 }}></div>
          <div className="absolute top-0 -right-4 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000" style={{ backgroundColor: colors.blobColor2 }}></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000" style={{ backgroundColor: colors.blobColor3 }}></div>
        </div>

        {/* Modern geometric shapes with dynamic colors */}
        <div className="absolute top-1/4 left-1/4 w-2 h-2 rounded-full animate-ping" style={{ backgroundColor: colors.dotColor }}></div>
        <div className="absolute top-1/3 right-1/3 w-1 h-1 rounded-full animate-ping animation-delay-1000" style={{ backgroundColor: colors.dotColor }}></div>
        <div className="absolute bottom-1/4 right-1/4 w-3 h-3 rounded-full animate-ping animation-delay-2000" style={{ backgroundColor: colors.dotColor }}></div>

        {/* Grid overlay */}
        <div
          className="absolute inset-0 opacity-10"
          style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${colors.primaryText.replace('#', '')}' fill-opacity='0.1'%3E%3Cpath d='M40 40H0V0h40v40zM20 20H0v20h20V20zm20-20H20v20h20V0z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
          }}
        ></div>
      </div>

      {/* Main content with modern grid layout */}
      <div className="relative z-10 min-h-screen">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid lg:grid-cols-12 gap-8 items-center min-h-screen py-20">

            {/* Left content area */}
            <div className="lg:col-span-7 space-y-8">
              {/* Trust indicators with dynamic colors */}
              <div className="flex flex-wrap gap-3 justify-center lg:justify-start">
                <div className="backdrop-blur-md rounded-full px-4 py-2 border" style={{ backgroundColor: colors.statsBg, borderColor: colors.inputBorder }}>
                  <div className="flex items-center space-x-2" style={{ color: colors.statsText }}>
                    <Home className="w-4 h-4" />
                    <span className="text-sm font-semibold">2K+ Kost</span>
                  </div>
                </div>
                <div className="backdrop-blur-md rounded-full px-4 py-2 border" style={{ backgroundColor: colors.statsBg, borderColor: colors.inputBorder }}>
                  <div className="flex items-center space-x-2" style={{ color: colors.statsText }}>
                    <Users className="w-4 h-4" />
                    <span className="text-sm font-semibold">50K+ Penghuni</span>
                  </div>
                </div>
                <div className="rounded-full px-4 py-2" style={{ backgroundColor: colors.buttonBg }}>
                  <div className="flex items-center space-x-2" style={{ color: colors.buttonText }}>
                    <Star className="w-4 h-4 fill-current" />
                    <span className="text-sm font-bold">4.8 Rating</span>
                  </div>
                </div>
              </div>

              {/* Modern headline with dynamic colors */}
              <div className="text-left space-y-6">
                <div className="space-y-2">
                  <h1 className="text-5xl md:text-7xl lg:text-8xl font-black leading-none tracking-tight" style={{ color: colors.primaryText }}>
                    Temukan
                  </h1>
                  <div className="flex items-center space-x-4">
                    <h1 className="text-5xl md:text-7xl lg:text-8xl font-black text-transparent bg-clip-text leading-none" style={{ backgroundImage: `linear-gradient(135deg, ${colors.buttonBg} 0%, ${colors.primaryText} 100%)` }}>
                      Kost
                    </h1>
                    <div className="hidden lg:block w-16 h-1 rounded-full" style={{ backgroundColor: colors.buttonBg }}></div>
                  </div>
                  <h1 className="text-4xl md:text-6xl lg:text-7xl font-light leading-none italic" style={{ color: colors.secondaryText }}>
                    Impianmu ✨
                  </h1>
                </div>

                <div className="space-y-4 max-w-lg">
                  <p className="text-xl md:text-2xl font-medium leading-relaxed" style={{ color: colors.secondaryText }}>
                    Platform <span className="font-bold" style={{ color: colors.primaryText }}>terpercaya</span> untuk mencari kost berkualitas
                  </p>
                  <p className="text-lg leading-relaxed" style={{ color: colors.secondaryText, opacity: 0.8 }}>
                    Ribuan pilihan kost di seluruh Indonesia dengan harga transparan dan lokasi strategis
                  </p>
                </div>
              </div>

              {/* Modern search form with dynamic colors */}
              <div className="mt-8">
                <form onSubmit={handleSearch} className="backdrop-blur-xl rounded-3xl p-6 border shadow-2xl" style={{ backgroundColor: colors.cardBg, borderColor: colors.inputBorder }}>
                  <div className="space-y-4">
                    {/* Location search */}
                    <div>
                      <label htmlFor="location" className="block text-sm font-semibold mb-3" style={{ color: colors.primaryText, opacity: 0.9 }}>
                        📍 Lokasi atau Area
                      </label>
                      <div className="relative">
                        <MapPin className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5" style={{ color: colors.primaryText, opacity: 0.6 }} />
                        <input
                          id="location"
                          type="text"
                          value={searchQuery}
                          onChange={(e) => setSearchQuery(e.target.value)}
                          placeholder="Cari berdasarkan kota, kampus, atau alamat..."
                          className="w-full pl-12 pr-4 py-4 backdrop-blur-sm border rounded-2xl focus:ring-2 focus:ring-blue-400 transition-all duration-300"
                          style={{
                            backgroundColor: colors.inputBg,
                            borderColor: colors.inputBorder,
                            color: colors.inputText
                          }}
                        />
                      </div>
                    </div>

                    {/* Property type selector with dynamic colors */}
                    <div>
                      <label htmlFor="property-type" className="block text-sm font-semibold mb-3" style={{ color: colors.primaryText, opacity: 0.9 }}>
                        🏠 Tipe Kost
                      </label>
                      <div className="relative">
                        <button
                          type="button"
                          onClick={() => setIsPropertyTypeOpen(!isPropertyTypeOpen)}
                          className="w-full px-4 py-4 backdrop-blur-sm border rounded-2xl text-left focus:ring-2 focus:ring-blue-400 flex items-center justify-between transition-all duration-300"
                          style={{
                            backgroundColor: colors.inputBg,
                            borderColor: colors.inputBorder,
                            color: colors.inputText
                          }}
                        >
                          <span className={selectedPropertyType ? 'font-medium' : ''} style={{ color: selectedPropertyType ? colors.inputText : `${colors.inputText}99` }}>
                            {getSelectedPropertyTypeLabel()}
                          </span>
                          <ChevronDown className={`w-5 h-5 transition-transform duration-300 ${isPropertyTypeOpen ? 'rotate-180' : ''}`} style={{ color: `${colors.inputText}99` }} />
                        </button>

                        {/* Modern dropdown menu with dynamic colors */}
                        {isPropertyTypeOpen && (
                          <div className="absolute top-full left-0 right-0 mt-2 backdrop-blur-xl border rounded-2xl shadow-2xl z-20 overflow-hidden" style={{ backgroundColor: colors.cardBg, borderColor: colors.inputBorder }}>
                            {propertyTypes.map((type) => (
                              <button
                                key={type.id}
                                type="button"
                                onClick={() => handlePropertyTypeSelect(type)}
                                className="w-full px-6 py-4 text-left flex items-center space-x-3 transition-all duration-300 group"
                                onMouseEnter={(e) => e.currentTarget.style.backgroundColor = colors.inputBg}
                                onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                              >
                                <div className="transition-colors" style={{ color: colors.primaryText }}>
                                  {type.icon}
                                </div>
                                <div>
                                  <div className="font-semibold" style={{ color: colors.primaryText }}>{type.label}</div>
                                  <div className="text-sm" style={{ color: colors.secondaryText }}>{type.description}</div>
                                </div>
                              </button>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Modern search button with dynamic colors */}
                    <div className="pt-2">
                      <button
                        type="submit"
                        className="w-full font-bold py-4 px-8 rounded-2xl transition-all duration-300 flex items-center justify-center space-x-3 shadow-2xl hover:scale-105 transform"
                        style={{
                          backgroundColor: colors.buttonBg,
                          color: colors.buttonText,
                          boxShadow: `0 25px 50px -12px ${colors.buttonBg}40`
                        }}
                        onMouseEnter={(e) => {
                          e.currentTarget.style.backgroundColor = colors.buttonHover;
                          e.currentTarget.style.boxShadow = `0 25px 50px -12px ${colors.buttonBg}66`;
                        }}
                        onMouseLeave={(e) => {
                          e.currentTarget.style.backgroundColor = colors.buttonBg;
                          e.currentTarget.style.boxShadow = `0 25px 50px -12px ${colors.buttonBg}40`;
                        }}
                      >
                        <Search className="w-6 h-6" />
                        <span className="text-lg">Cari Kost</span>
                      </button>
                    </div>

                    {/* Quick filters with dynamic colors */}
                    <div className="mt-6 pt-6" style={{ borderTop: `1px solid ${colors.inputBorder}` }}>
                      <p className="text-sm mb-4 font-medium" style={{ color: colors.secondaryText, opacity: 0.8 }}>🔥 Pencarian populer:</p>
                      <div className="flex flex-wrap gap-2">
                        {[
                          'Kost dekat UI',
                          'Jakarta Selatan',
                          'Kost murah Depok',
                          'Bandung',
                          'dekat ITB',
                          'Yogyakarta'
                        ].map((tag) => (
                          <button
                            key={tag}
                            type="button"
                            onClick={() => setSearchQuery(tag)}
                            className="px-4 py-2 text-sm rounded-full transition-all duration-300 border backdrop-blur-sm"
                            style={{
                              backgroundColor: colors.inputBg,
                              borderColor: colors.inputBorder,
                              color: colors.inputText,
                              opacity: 0.9
                            }}
                            onMouseEnter={(e) => {
                              e.currentTarget.style.backgroundColor = colors.buttonBg;
                              e.currentTarget.style.color = colors.buttonText;
                              e.currentTarget.style.opacity = '1';
                            }}
                            onMouseLeave={(e) => {
                              e.currentTarget.style.backgroundColor = colors.inputBg;
                              e.currentTarget.style.color = colors.inputText;
                              e.currentTarget.style.opacity = '0.9';
                            }}
                          >
                            {tag}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </form>
              </div>
            </div>

              {/* Right side content - Visual elements with dynamic colors */}
              <div className="lg:col-span-5 relative">
                <div className="relative h-96 lg:h-full">
                  {/* Floating cards with dynamic colors */}
                  <div className="absolute top-0 right-0 backdrop-blur-xl rounded-3xl p-6 border transform rotate-3 hover:rotate-0 transition-transform duration-500" style={{ backgroundColor: colors.cardBg, borderColor: colors.inputBorder }}>
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: colors.buttonBg }}>
                        <Home className="w-6 h-6" style={{ color: colors.buttonText }} />
                      </div>
                      <div>
                        <div className="font-bold text-lg" style={{ color: colors.primaryText }}>2000+</div>
                        <div className="text-sm" style={{ color: colors.secondaryText, opacity: 0.7 }}>Kost Tersedia</div>
                      </div>
                    </div>
                  </div>

                  <div className="absolute top-32 left-0 backdrop-blur-xl rounded-3xl p-6 border transform -rotate-3 hover:rotate-0 transition-transform duration-500" style={{ backgroundColor: colors.cardBg, borderColor: colors.inputBorder }}>
                    <div className="flex items-center space-x-3 mb-3">
                      <div className="w-12 h-12 rounded-full flex items-center justify-center" style={{ backgroundColor: colors.buttonBg }}>
                        <Users className="w-6 h-6" style={{ color: colors.buttonText }} />
                      </div>
                      <div>
                        <div className="font-bold text-lg" style={{ color: colors.primaryText }}>50K+</div>
                        <div className="text-sm" style={{ color: colors.secondaryText, opacity: 0.7 }}>Penghuni Bahagia</div>
                      </div>
                    </div>
                  </div>

                  <div className="absolute bottom-0 right-8 rounded-3xl p-6 transform rotate-2 hover:rotate-0 transition-transform duration-500" style={{ backgroundColor: colors.buttonBg }}>
                    <div className="flex items-center space-x-3">
                      <Star className="w-8 h-8 fill-current" style={{ color: colors.buttonText }} />
                      <div>
                        <div className="font-bold text-xl" style={{ color: colors.buttonText }}>4.8</div>
                        <div className="text-sm font-medium" style={{ color: colors.buttonText, opacity: 0.8 }}>Rating</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

      {/* Scroll indicator */}
      <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce" style={{ color: colors.secondaryText, opacity: 0.6 }}>
        <div className="flex flex-col items-center space-y-2">
          <span className="text-sm">Scroll untuk melihat lebih banyak</span>
          <ChevronDown className="w-5 h-5" />
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
