import React, { useState } from 'react';
import { Menu, X, Home, Phone, HelpCircle, User } from 'lucide-react';
import SatuAtapLogo from '../components/SatuAtapLogo';

// Header props interface (simplified - no search)
interface HeaderProps {}

// Navigation link interface
interface NavLink {
  label: string;
  href: string;
  id: string;
  icon?: React.ReactElement;
}

// Main navigation links - simplified
const navLinks: NavLink[] = [
  { label: 'Beranda', href: '#', id: 'home', icon: <Home className="w-4 h-4" /> },
  { label: 'Tentang', href: '#about', id: 'about', icon: <HelpCircle className="w-4 h-4" /> },
  { label: 'Kontak', href: '#contact', id: 'contact', icon: <Phone className="w-4 h-4" /> },
];

const Header: React.FC<HeaderProps> = () => {
  const [mobileOpen, setMobileOpen] = useState(false);

  // Handle navigation click
  const handleNavClick = (e: React.MouseEvent<HTMLAnchorElement>, href: string) => {
    e.preventDefault();
    if (href.startsWith('#')) {
      // Handle anchor links (if any sections exist)
      const element = document.querySelector(href);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    } else {
      // Handle external links
      window.location.href = href;
    }
    setMobileOpen(false);
  };

  // Get colors for light theme only
  const colors = {
    headerBg: 'rgba(221, 230, 237, 0.95)', // #DDE6ED with opacity
    borderColor: 'rgba(157, 178, 191, 0.3)', // #9DB2BF with opacity
    primaryText: '#27374D',
    secondaryText: '#526D82',
    accentColor: '#9DB2BF',
    logoContainer: 'rgba(39, 55, 77, 0.1)',
    buttonBg: 'rgba(157, 178, 191, 0.3)',
    buttonHoverBg: 'rgba(39, 55, 77, 0.1)',
    primaryButton: '#27374D',
    primaryButtonText: '#DDE6ED',
    primaryButtonHover: '#526D82'
  };



  return (
    <header className="fixed top-4 left-4 right-4 z-50">
      <div className="mx-auto max-w-7xl">
        <div
          className="rounded-2xl shadow-xl backdrop-blur-lg border px-6 py-4"
          style={{
            backgroundColor: colors.headerBg,
            borderColor: colors.borderColor
          }}
        >
          <div className="flex items-center justify-between">
            {/* Logo Section */}
            <div className="flex items-center space-x-3">
              <div className="p-1 rounded-xl" style={{ backgroundColor: colors.logoContainer }}>
                <SatuAtapLogo size="sm" className="w-8 h-8" />
              </div>
              <div className="flex flex-col">
                <h1 className="text-lg font-bold" style={{ color: colors.primaryText }}>
                  Satu Atap
                </h1>
                <p className="hidden sm:block text-xs" style={{ color: colors.secondaryText }}>
                  Platform Kost Terpercaya
                </p>
              </div>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex items-center space-x-2">
              {navLinks.map((link) => (
                <a
                  key={link.id}
                  href={link.href}
                  onClick={(e) => handleNavClick(e, link.href)}
                  className="flex items-center space-x-2 px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105"
                  style={{
                    color: colors.secondaryText,
                    backgroundColor: 'transparent'
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor = colors.buttonHoverBg;
                    e.currentTarget.style.color = colors.primaryText;
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = 'transparent';
                    e.currentTarget.style.color = colors.secondaryText;
                  }}
                >
                  {link.icon}
                  <span>{link.label}</span>
                </a>
              ))}
            </nav>

            {/* Right Section */}
            <div className="flex items-center space-x-3">
              {/* Auth Button */}
              <a
                href="/login"
                className="hidden sm:flex items-center space-x-2 px-5 py-2.5 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 shadow-md"
                style={{
                  backgroundColor: colors.primaryButton,
                  color: colors.primaryButtonText
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.primaryButtonHover;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = colors.primaryButton;
                }}
              >
                <User className="w-4 h-4" />
                <span>Masuk</span>
              </a>

              {/* Mobile Menu Button */}
              <button
                onClick={() => setMobileOpen(!mobileOpen)}
                className="md:hidden p-2 rounded-xl transition-all duration-200 hover:scale-105"
                style={{
                  backgroundColor: colors.buttonBg,
                  color: colors.secondaryText
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = colors.buttonHoverBg;
                  e.currentTarget.style.color = colors.primaryText;
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = colors.buttonBg;
                  e.currentTarget.style.color = colors.secondaryText;
                }}
                aria-label="Toggle menu"
              >
                {mobileOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Mobile Navigation */}
          {mobileOpen && (
            <div className="md:hidden mt-4 pt-4 border-t" style={{ borderColor: colors.borderColor }}>
              <nav className="space-y-2">
                {navLinks.map((link) => (
                  <a
                    key={link.id}
                    href={link.href}
                    onClick={(e) => handleNavClick(e, link.href)}
                    className="flex items-center space-x-3 px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200"
                    style={{
                      color: colors.secondaryText,
                      backgroundColor: 'transparent'
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.buttonHoverBg;
                      e.currentTarget.style.color = colors.primaryText;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = 'transparent';
                      e.currentTarget.style.color = colors.secondaryText;
                    }}
                  >
                    {link.icon}
                    <span>{link.label}</span>
                  </a>
                ))}
                <div className="pt-3">
                  <a
                    href="/login"
                    className="flex items-center space-x-2 px-4 py-3 rounded-xl text-sm font-medium w-full justify-center transition-all duration-200 shadow-md"
                    style={{
                      backgroundColor: colors.primaryButton,
                      color: colors.primaryButtonText
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.backgroundColor = colors.primaryButtonHover;
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.backgroundColor = colors.primaryButton;
                    }}
                  >
                    <User className="w-4 h-4" />
                    <span>Masuk</span>
                  </a>
                </div>
              </nav>
            </div>
          )}
        </div>
      </div>
    </header>
  );
};



export default Header;
