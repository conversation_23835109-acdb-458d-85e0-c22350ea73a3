[2025-07-11 19:23:05] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:23:06] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:25:25] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:25:26] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:25:44] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:25:44] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:25:55] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:25:55] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:06] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:06] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:23] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:23] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:40] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:41] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:26:54] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:26:55] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:27:20] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:27:21] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:28:51] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:28:51] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:30:09] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:30:09] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-11 19:44:36] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-11 19:44:36] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 03:48:51] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 03:48:52] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:03:15] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:03:15] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:16:27] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:16:28] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:16:29] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:16:29] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:42:35] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:42:35] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:43:12] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:43:12] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:43:13] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:43:14] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:49:09] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:49:09] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:49:29] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:49:29] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 04:52:57] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 04:52:57] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 05:05:05] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 05:05:05] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:08:28] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:08:28] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:10:04] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:10:04] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:15:45] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:15:46] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:17:17] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:17:17] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:17:49] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:17:50] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:18:32] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:18:33] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:22:36] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:22:37] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:25:03] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:25:03] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:29:13] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:29:13] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:29:14] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:29:14] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:30:35] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 1)  
[2025-07-13 07:30:35] local.ERROR: Error fetching multiple availability: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'satuatap.kost_properties' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `kost_properties` where `id` = 2)  
[2025-07-13 07:38:55] local.ERROR: include(D:\Vicky\project baru\satu-atap\app\Http\Middleware\HandleAppearance.php): Failed to open stream: No such file or directory {"exception":"[object] (ErrorException(code: 0): include(D:\\Vicky\\project baru\\satu-atap\\app\\Http\\Middleware\\HandleAppearance.php): Failed to open stream: No such file or directory at D:\\Vicky\\project baru\\satu-atap\\vendor\\composer\\ClassLoader.php:576)
[stacktrace]
#0 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'include(D:\\\\Vick...', 'D:\\\\Vicky\\\\projec...', 576)
#1 D:\\Vicky\\project baru\\satu-atap\\vendor\\composer\\ClassLoader.php(576): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'include(D:\\\\Vick...', 'D:\\\\Vicky\\\\projec...', 576)
#2 D:\\Vicky\\project baru\\satu-atap\\vendor\\composer\\ClassLoader.php(576): include('D:\\\\Vicky\\\\projec...')
#3 D:\\Vicky\\project baru\\satu-atap\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\Vicky\\\\projec...')
#4 [internal function]: Composer\\Autoload\\ClassLoader->loadClass('App\\\\Http\\\\Middle...')
#5 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('App\\\\Http\\\\Middle...')
#6 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#7 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#8 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#9 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#10 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#11 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#13 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#14 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#19 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#26 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#27 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#28 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#29 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#30 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#31 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#51 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#52 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#53 D:\\Vicky\\project baru\\satu-atap\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#54 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Vicky\\\\projec...')
#55 {main}
"} 
[2025-07-13 07:39:03] local.ERROR: Target class [HandleAppearance] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [HandleAppearance] does not exist. at D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('HandleAppearanc...')
#1 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('HandleAppearanc...', Array, true)
#2 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('HandleAppearanc...', Array)
#3 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('HandleAppearanc...', Array)
#4 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('HandleAppearanc...')
#5 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#6 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#7 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#8 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#9 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#10 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#11 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#12 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#13 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#20 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#21 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#22 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#23 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#24 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#25 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#45 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#46 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#47 D:\\Vicky\\project baru\\satu-atap\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#48 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Vicky\\\\projec...')
#49 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"HandleAppearance\" does not exist at D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('HandleAppearanc...')
#1 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('HandleAppearanc...')
#2 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('HandleAppearanc...', Array, true)
#3 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('HandleAppearanc...', Array)
#4 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('HandleAppearanc...', Array)
#5 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(197): Illuminate\\Foundation\\Application->make('HandleAppearanc...')
#6 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#8 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#10 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(120): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(63): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#14 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#15 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(36): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#16 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#17 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(74): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#19 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(807): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#21 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#22 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(750): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#23 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#24 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#25 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#26 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(109): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(48): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\ValidatePathEncoding.php(26): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(208): Illuminate\\Http\\Middleware\\ValidatePathEncoding->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(126): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#46 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#47 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1219): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#48 D:\\Vicky\\project baru\\satu-atap\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#49 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Vicky\\\\projec...')
#50 {main}
"} 
[2025-07-13 07:39:03] local.ERROR: Target class [HandleAppearance] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [HandleAppearance] does not exist. at D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1019)
[stacktrace]
#0 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('HandleAppearanc...')
#1 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('HandleAppearanc...', Array, true)
#2 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('HandleAppearanc...', Array)
#3 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('HandleAppearanc...', Array)
#4 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('HandleAppearanc...')
#5 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#6 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\Vicky\\project baru\\satu-atap\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#8 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Vicky\\\\projec...')
#9 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"HandleAppearance\" does not exist at D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:1017)
[stacktrace]
#0 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1017): ReflectionClass->__construct('HandleAppearanc...')
#1 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(890): Illuminate\\Container\\Container->build('HandleAppearanc...')
#2 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1077): Illuminate\\Container\\Container->resolve('HandleAppearanc...', Array, true)
#3 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(821): Illuminate\\Foundation\\Application->resolve('HandleAppearanc...', Array)
#4 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1057): Illuminate\\Container\\Container->make('HandleAppearanc...', Array)
#5 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(257): Illuminate\\Foundation\\Application->make('HandleAppearanc...')
#6 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(215): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#7 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1221): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#8 D:\\Vicky\\project baru\\satu-atap\\public\\index.php(20): Illuminate\\Foundation\\Application->handleRequest(Object(Illuminate\\Http\\Request))
#9 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\resources\\server.php(23): require_once('D:\\\\Vicky\\\\projec...')
#10 {main}
"} 
[2025-07-13 07:39:04] local.ERROR: Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\Vicky\project baru\satu-atap\vendor\symfony\http-foundation\Response.php:387) in D:\Vicky\project baru\satu-atap\vendor\symfony\http-foundation\Response.php:322
Stack trace:
#0 D:\Vicky\project baru\satu-atap\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\Vicky\\projec...', 322)
#1 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(2, 'Cannot modify h...', 'D:\\Vicky\\projec...', 322)
#2 D:\Vicky\project baru\satu-atap\vendor\symfony\http-foundation\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\Vicky\project baru\satu-atap\vendor\symfony\http-foundation\Response.php(401): Symfony\Component\HttpFoundation\Response->sendHeaders()
#4 D:\Vicky\project baru\satu-atap\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(219): Symfony\Component\HttpFoundation\Response->send()
#5 D:\Vicky\project baru\satu-atap\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(196): Illuminate\Foundation\Bootstrap\HandleExceptions->renderHttpResponse(Object(Illuminate\Contracts\Container\BindingResolutionException))
#6 D:\Vicky\project baru\satu-atap\vendor\laravel\framework\src\Illuminate\Foundation\Bootstrap\HandleExceptions.php(256): Illuminate\Foundation\Bootstrap\HandleExceptions->handleException(Object(Illuminate\Contracts\Container\BindingResolutionException))
#7 [internal function]: Illuminate\Foundation\Bootstrap\HandleExceptions->Illuminate\Foundation\Bootstrap\{closure}(Object(Illuminate\Contracts\Container\BindingResolutionException))
#8 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ErrorException: Cannot modify header information - headers already sent by (output started at D:\\Vicky\\project baru\\satu-atap\\vendor\\symfony\\http-foundation\\Response.php:387) in D:\\Vicky\\project baru\\satu-atap\\vendor\\symfony\\http-foundation\\Response.php:322
Stack trace:
#0 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Cannot modify h...', 'D:\\\\Vicky\\\\projec...', 322)
#1 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(2, 'Cannot modify h...', 'D:\\\\Vicky\\\\projec...', 322)
#2 D:\\Vicky\\project baru\\satu-atap\\vendor\\symfony\\http-foundation\\Response.php(322): header('HTTP/1.1 500 In...', true, 500)
#3 D:\\Vicky\\project baru\\satu-atap\\vendor\\symfony\\http-foundation\\Response.php(401): Symfony\\Component\\HttpFoundation\\Response->sendHeaders()
#4 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(219): Symfony\\Component\\HttpFoundation\\Response->send()
#5 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(196): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->renderHttpResponse(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#6 D:\\Vicky\\project baru\\satu-atap\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Bootstrap\\HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleException(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#7 [internal function]: Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}(Object(Illuminate\\Contracts\\Container\\BindingResolutionException))
#8 {main}
  thrown at D:\\Vicky\\project baru\\satu-atap\\vendor\\symfony\\http-foundation\\Response.php:322)
[stacktrace]
#0 {main}
"} 
