### <PERSON><PERSON><PERSON> Landing Page dan SIM

Keduanya terhubung secara teknis melalui **API (Application Programming Interface)** untuk menciptakan alur kerja yang otomatis dan efisien:

1.  **<PERSON><PERSON> ke Landing Page (Sinkronisasi Data):**
    * Status ketersediaan kamar yang dikelola Admin/Pemilik di dalam **SIM** akan secara *real-time* ditampilkan di **Landing Page**. <PERSON><PERSON>, calon penyewa selalu melihat informasi yang akurat.

2.  **Dari <PERSON> Page ke SIM (Penerusan Prospek):**
    * Ketika seorang calon penyewa mengisi formulir "Jadwalkan Kunjungan" di **Landing Page**, datanya tidak hanya masuk ke email, tetapi langsung dikirim ke **SIM**.
    * Data ini otomatis membuat entri "prospek baru" di dalam SIM, dan Admin akan mendapatkan notifikasi untuk segera menindaklanjutinya.

Singkatnya, **Landing Page** bertugas "menangkap" calon penyewa, dan **SIM** bertugas "mengelola" mereka secara efisien dari saat menjadi prospek hingg