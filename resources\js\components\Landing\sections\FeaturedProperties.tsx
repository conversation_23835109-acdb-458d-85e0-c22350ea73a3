import React, { useState } from 'react';
import {
  Star,
  MapPin,
  Wifi,
  Shield,
  Eye,
  Heart,
  MessageCircle,
  ChevronLeft,
  ChevronRight,
  SlidersHorizontal
} from 'lucide-react';
import { KostProperty } from '@/types';
import AvailabilityIndicator from '@/components/ui/AvailabilityIndicator';

// Mock data for featured properties (will be replaced with API call)
const mockFeaturedProperties: KostProperty[] = [
  {
    id: '1',
    title: 'Kost Eksklusif Dekat UI Depok',
    description: 'Kost modern dengan fasilitas lengkap, lokasi strategis dekat Universitas Indonesia',
    price_monthly: 2500000,
    property_type: 'campur',
    room_type: 'single',
    available_rooms: 3,
    total_rooms: 20,
    rating: 4.8,
    review_count: 124,
    is_featured: true,
    is_verified: true,
    images: [
      {
        id: '1',
        url: 'https://images.unsplash.com/photo-1555854877-bab0e564b8d5?w=800',
        alt: 'Kamar kost modern',
        is_primary: true,
        order: 1
      },
      {
        id: '2',
        url: 'https://images.unsplash.com/photo-1586023492125-27b2c045efd7?w=800',
        alt: 'Ruang bersama',
        is_primary: false,
        order: 2
      }
    ],
    amenities: [
      { id: '1', name: 'WiFi', icon: 'Wifi', category: 'connectivity', is_popular: true },
      { id: '2', name: 'Parkir', icon: 'Car', category: 'basic', is_popular: true },
      { id: '3', name: 'Keamanan 24 Jam', icon: 'Shield', category: 'security', is_popular: true }
    ],
    location: {
      id: '1',
      address: 'Jl. Margonda Raya No. 123',
      district: 'Beji',
      city: 'Depok',
      province: 'Jawa Barat',
      postal_code: '16424',
      latitude: -6.3728,
      longitude: 106.8317,
      nearby_landmarks: ['Universitas Indonesia', 'Stasiun UI', 'Mall Depok']
    },
    owner: {
      id: '1',
      name: 'Ibu Sari',
      phone: '+6281234567890',
      response_rate: 95,
      response_time: '< 1 jam'
    },
    rules: ['Tidak merokok', 'Tidak membawa tamu menginap'],
    facilities: ['AC', 'Kamar Mandi Dalam', 'Lemari', 'Kasur'],
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  },
  {
    id: '2',
    title: 'Kost Putri Nyaman Jakarta Selatan',
    description: 'Kost khusus putri dengan keamanan terjamin dan fasilitas premium',
    price_monthly: 1800000,
    property_type: 'putri',
    room_type: 'single',
    available_rooms: 5,
    total_rooms: 15,
    rating: 4.6,
    review_count: 89,
    is_featured: true,
    is_verified: true,
    images: [
      {
        id: '3',
        url: 'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
        alt: 'Kamar kost putri',
        is_primary: true,
        order: 1
      }
    ],
    amenities: [
      { id: '1', name: 'WiFi', icon: 'Wifi', category: 'connectivity', is_popular: true },
      { id: '4', name: 'Dapur Bersama', icon: 'Users', category: 'basic', is_popular: true }
    ],
    location: {
      id: '2',
      address: 'Jl. Kemang Raya No. 456',
      district: 'Kemang',
      city: 'Jakarta Selatan',
      province: 'DKI Jakarta',
      postal_code: '12560',
      latitude: -6.2615,
      longitude: 106.8106,
      nearby_landmarks: ['Mall Kemang Village', 'Stasiun MRT Cipete Raya']
    },
    owner: {
      id: '2',
      name: 'Pak Budi',
      phone: '+6281234567891',
      response_rate: 92,
      response_time: '< 2 jam'
    },
    rules: ['Khusus putri', 'Jam malam 22:00'],
    facilities: ['AC', 'Kamar Mandi Dalam', 'WiFi', 'Lemari'],
    created_at: '2024-01-01',
    updated_at: '2024-01-01'
  }
];

interface FeaturedPropertiesProps {
  properties?: KostProperty[];
  loading?: boolean;
  onPropertyClick?: (property: KostProperty) => void;
  onContactOwner?: (property: KostProperty) => void;
  isDark?: boolean;
}

const FeaturedProperties: React.FC<FeaturedPropertiesProps> = ({
  properties = mockFeaturedProperties,
  loading = false,
  onPropertyClick,
  onContactOwner,
  isDark = false
}) => {
  // Get colors based on theme
  const getColors = () => {
    if (isDark) {
      return {
        sectionBg: '#1E201E',
        cardBg: '#3C3D37',
        primaryText: '#ECDFCC',
        secondaryText: '#697565',
        accentColor: '#ECDFCC',
        borderColor: '#697565',
        buttonBg: '#ECDFCC',
        buttonText: '#1E201E',
        buttonHover: 'rgba(236, 223, 204, 0.9)',
        badgeBg: 'rgba(236, 223, 204, 0.1)',
        badgeText: '#ECDFCC',
        priceBg: 'rgba(105, 117, 101, 0.2)',
        priceText: '#ECDFCC'
      };
    } else {
      return {
        sectionBg: '#DDE6ED',
        cardBg: '#FFFFFF',
        primaryText: '#27374D',
        secondaryText: '#526D82',
        accentColor: '#526D82',
        borderColor: '#9DB2BF',
        buttonBg: '#27374D',
        buttonText: '#DDE6ED',
        buttonHover: '#526D82',
        badgeBg: 'rgba(39, 55, 77, 0.1)',
        badgeText: '#27374D',
        priceBg: 'rgba(82, 109, 130, 0.1)',
        priceText: '#27374D'
      };
    }
  };

  const colors = getColors();
  const [currentImageIndex, setCurrentImageIndex] = useState<Record<string, number>>({});
  const [favorites, setFavorites] = useState<Set<string>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  // Handle image navigation
  const handleImageNavigation = (propertyId: string, direction: 'prev' | 'next', totalImages: number) => {
    setCurrentImageIndex(prev => {
      const current = prev[propertyId] || 0;
      let newIndex;
      
      if (direction === 'next') {
        newIndex = current >= totalImages - 1 ? 0 : current + 1;
      } else {
        newIndex = current <= 0 ? totalImages - 1 : current - 1;
      }
      
      return { ...prev, [propertyId]: newIndex };
    });
  };

  // Toggle favorite
  const toggleFavorite = (propertyId: string) => {
    setFavorites(prev => {
      const newFavorites = new Set(prev);
      if (newFavorites.has(propertyId)) {
        newFavorites.delete(propertyId);
      } else {
        newFavorites.add(propertyId);
      }
      return newFavorites;
    });
  };

  // Format price
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(price);
  };

  // Get property type badge color
  const getPropertyTypeBadge = (type: string) => {
    const badges = {
      putra: { label: 'Putra', color: 'bg-blue-100 text-blue-800' },
      putri: { label: 'Putri', color: 'bg-pink-100 text-pink-800' },
      campur: { label: 'Campur', color: 'bg-green-100 text-green-800' }
    };
    return badges[type as keyof typeof badges] || badges.campur;
  };

  if (loading) {
    return (
      <section className="py-16" style={{ backgroundColor: colors.sectionBg }}>
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="h-8 rounded w-64 mx-auto mb-4 animate-pulse" style={{ backgroundColor: colors.borderColor }}></div>
            <div className="h-4 rounded w-96 mx-auto animate-pulse" style={{ backgroundColor: colors.borderColor }}></div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[1, 2, 3].map((i) => (
              <div key={i} className="rounded-2xl shadow-lg overflow-hidden animate-pulse" style={{ backgroundColor: colors.cardBg }}>
                <div className="h-64" style={{ backgroundColor: colors.borderColor }}></div>
                <div className="p-6">
                  <div className="h-4 rounded w-3/4 mb-2" style={{ backgroundColor: colors.borderColor }}></div>
                  <div className="h-4 rounded w-1/2 mb-4" style={{ backgroundColor: colors.borderColor }}></div>
                  <div className="h-8 rounded w-full" style={{ backgroundColor: colors.borderColor }}></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="py-20 relative overflow-hidden" style={{ backgroundColor: colors.sectionBg }}>
      {/* Background decorative elements with dynamic colors */}
      <div className="absolute top-0 left-0 w-72 h-72 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2" style={{ backgroundColor: `${colors.accentColor}33` }}></div>
      <div className="absolute bottom-0 right-0 w-96 h-96 rounded-full blur-3xl translate-x-1/2 translate-y-1/2" style={{ backgroundColor: `${colors.secondaryText}26` }}></div>

      <div className="container mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Modern section header with dynamic colors */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center space-x-2 rounded-full px-6 py-2 mb-6" style={{ backgroundColor: colors.badgeBg }}>
            <span className="font-semibold text-sm" style={{ color: colors.badgeText }}>✨ PILIHAN TERBAIK</span>
          </div>
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-black mb-6 leading-tight" style={{ color: colors.primaryText }}>
            Kost <span className="text-transparent bg-clip-text" style={{ backgroundImage: `linear-gradient(135deg, ${colors.accentColor} 0%, ${colors.secondaryText} 100%)` }}>Pilihan</span>
            <br />
            <span className="text-3xl md:text-4xl lg:text-5xl font-light italic">Terbaik ⭐</span>
          </h2>
          <p className="text-xl max-w-2xl mx-auto leading-relaxed" style={{ color: colors.secondaryText }}>
            Temukan kost berkualitas dengan fasilitas lengkap dan lokasi strategis yang sudah dipilih khusus untuk kamu
          </p>
        </div>

        {/* Modern filter toggle with dynamic colors */}
        <div className="flex flex-col sm:flex-row justify-between items-center mb-12 space-y-4 sm:space-y-0">
          <div className="flex items-center space-x-3">
            <div className="rounded-full p-2" style={{ backgroundColor: colors.buttonBg }}>
              <Eye className="w-4 h-4" style={{ color: colors.buttonText }} />
            </div>
            <span className="font-medium" style={{ color: colors.secondaryText }}>
              Menampilkan <span className="font-bold" style={{ color: colors.primaryText }}>{properties.length}</span> properti unggulan
            </span>
          </div>
          <button
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-3 px-6 py-3 backdrop-blur-sm border-2 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl group"
            style={{
              backgroundColor: colors.cardBg,
              borderColor: colors.borderColor
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.buttonBg;
              e.currentTarget.style.borderColor = colors.accentColor;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = colors.cardBg;
              e.currentTarget.style.borderColor = colors.borderColor;
            }}
          >
            <SlidersHorizontal className="w-5 h-5 group-hover:rotate-180 transition-transform duration-300" style={{ color: colors.secondaryText }} />
            <span className="font-semibold" style={{ color: colors.primaryText }}>Filter</span>
          </button>
        </div>

        {/* Modern properties grid with staggered layout */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
          {properties.map((property, index) => {
            const currentImage = currentImageIndex[property.id] || 0;
            const propertyTypeBadge = getPropertyTypeBadge(property.property_type);
            const isFavorite = favorites.has(property.id);

            return (
              <div
                key={property.id}
                className={`group relative backdrop-blur-sm rounded-3xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:scale-[1.03] hover:-rotate-1 ${
                  index % 3 === 1 ? 'lg:mt-8' : index % 3 === 2 ? 'lg:-mt-4' : ''
                } border`}
                style={{
                  backgroundColor: colors.cardBg,
                  borderColor: colors.borderColor
                }}
              >
                {/* Modern image carousel */}
                <div className="relative h-72 overflow-hidden rounded-t-3xl">
                  {property.images.length > 0 && (
                    <>
                      <img
                        src={property.images[currentImage]?.url}
                        alt={property.images[currentImage]?.alt}
                        className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                      />

                      {/* Gradient overlay */}
                      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

                      {/* Modern image navigation */}
                      {property.images.length > 1 && (
                        <>
                          <button
                            onClick={() => handleImageNavigation(property.id, 'prev', property.images.length)}
                            className="absolute left-3 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-md text-white p-3 rounded-full hover:bg-white/30 transition-all duration-300 opacity-0 group-hover:opacity-100"
                          >
                            <ChevronLeft className="w-5 h-5" />
                          </button>
                          <button
                            onClick={() => handleImageNavigation(property.id, 'next', property.images.length)}
                            className="absolute right-3 top-1/2 transform -translate-y-1/2 bg-white/20 backdrop-blur-md text-white p-3 rounded-full hover:bg-white/30 transition-all duration-300 opacity-0 group-hover:opacity-100"
                          >
                            <ChevronRight className="w-5 h-5" />
                          </button>

                          {/* Modern image indicators */}
                          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
                            {property.images.map((_, index) => (
                              <div
                                key={index}
                                className={`w-2 h-2 rounded-full transition-all duration-300 ${
                                  index === currentImage ? 'bg-white w-6' : 'bg-white/60'
                                }`}
                              />
                            ))}
                          </div>
                        </>
                      )}
                    </>
                  )}

                  {/* Modern badges with dynamic colors */}
                  <div className="absolute top-4 left-4 flex flex-col space-y-2">
                    <span className="px-3 py-1.5 rounded-full text-xs font-bold backdrop-blur-md border" style={{ backgroundColor: colors.badgeBg, borderColor: colors.borderColor, color: colors.badgeText }}>
                      {propertyTypeBadge.label}
                    </span>
                    {property.is_verified && (
                      <span className="px-3 py-1.5 rounded-full text-xs font-bold flex items-center space-x-1 shadow-lg" style={{ backgroundColor: colors.buttonBg, color: colors.buttonText }}>
                        <Shield className="w-3 h-3" />
                        <span>Verified ✓</span>
                      </span>
                    )}
                  </div>

                  {/* Modern favorite button with dynamic colors */}
                  <button
                    onClick={() => toggleFavorite(property.id)}
                    className="absolute top-4 right-4 p-3 backdrop-blur-md rounded-full transition-all duration-300 group"
                    style={{ backgroundColor: `${colors.cardBg}80` }}
                    onMouseEnter={(e) => e.currentTarget.style.backgroundColor = `${colors.cardBg}CC`}
                    onMouseLeave={(e) => e.currentTarget.style.backgroundColor = `${colors.cardBg}80`}
                  >
                    <Heart className={`w-5 h-5 transition-all duration-300 ${
                      isFavorite
                        ? 'scale-110'
                        : 'group-hover:scale-110'
                    }`}
                    style={{
                      color: isFavorite ? '#DC2626' : colors.secondaryText,
                      fill: isFavorite ? '#DC2626' : 'none'
                    }} />
                  </button>

                  {/* Modern availability indicator with dynamic colors */}
                  <div className="absolute bottom-4 right-4">
                    <div style={{ backgroundColor: `${colors.primaryText}CC`, borderColor: colors.borderColor }} className="backdrop-blur-md rounded-full border">
                      <AvailabilityIndicator
                        propertyId={property.id}
                        showDetails={false}
                        className="backdrop-blur-md rounded-full border"
                      />
                    </div>
                  </div>
                </div>

                {/* Modern property details with dynamic colors */}
                <div className="p-6 space-y-4">
                  {/* Title and rating */}
                  <div>
                    <h3 className="text-xl font-bold mb-2 line-clamp-2 transition-colors duration-300" style={{ color: colors.primaryText }}>
                      {property.title}
                    </h3>
                    <div className="flex items-center space-x-3">
                      <div className="flex items-center space-x-1 rounded-full px-3 py-1" style={{ backgroundColor: colors.buttonBg }}>
                        <Star className="w-4 h-4" style={{ fill: colors.buttonText, color: colors.buttonText }} />
                        <span className="text-sm font-bold" style={{ color: colors.buttonText }}>{property.rating}</span>
                      </div>
                      <span className="text-sm font-medium" style={{ color: colors.secondaryText }}>({property.review_count} ulasan)</span>
                    </div>
                  </div>

                  {/* Location */}
                  <div className="flex items-center space-x-2">
                    <div className="rounded-full p-1.5" style={{ backgroundColor: colors.badgeBg }}>
                      <MapPin className="w-4 h-4" style={{ color: colors.secondaryText }} />
                    </div>
                    <span className="text-sm font-medium" style={{ color: colors.secondaryText }}>{property.location.district}, {property.location.city}</span>
                  </div>

                  {/* Modern amenities with dynamic colors */}
                  <div className="flex flex-wrap gap-2">
                    {property.amenities.slice(0, 3).map((amenity) => (
                      <div key={amenity.id} className="flex items-center space-x-1.5 border px-3 py-1.5 rounded-full text-xs font-medium" style={{ backgroundColor: colors.badgeBg, borderColor: colors.borderColor, color: colors.secondaryText }}>
                        <Wifi className="w-3 h-3" />
                        <span>{amenity.name}</span>
                      </div>
                    ))}
                    {property.amenities.length > 3 && (
                      <span className="text-xs font-medium px-2 py-1 rounded-full" style={{ color: colors.secondaryText, backgroundColor: colors.badgeBg }}>
                        +{property.amenities.length - 3} lainnya
                      </span>
                    )}
                  </div>

                  {/* Modern price display with dynamic colors */}
                  <div className="rounded-2xl p-4 border" style={{ backgroundColor: colors.priceBg, borderColor: colors.borderColor }}>
                    <div className="text-2xl font-black text-transparent bg-clip-text" style={{ backgroundImage: `linear-gradient(135deg, ${colors.primaryText} 0%, ${colors.accentColor} 100%)` }}>
                      {formatPrice(property.price_monthly)}
                    </div>
                    <div className="text-sm font-medium" style={{ color: colors.secondaryText }}>per bulan • All inclusive</div>
                  </div>

                  {/* Modern action buttons with dynamic colors */}
                  <div className="flex space-x-3 pt-2">
                    <button
                      onClick={() => onPropertyClick?.(property)}
                      className="flex-1 py-3 px-4 rounded-2xl transition-all duration-300 flex items-center justify-center space-x-2 font-semibold shadow-lg hover:shadow-xl transform hover:scale-105"
                      style={{ backgroundColor: colors.buttonBg, color: colors.buttonText }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = colors.buttonHover;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = colors.buttonBg;
                      }}
                    >
                      <Eye className="w-5 h-5" />
                      <span>Lihat Detail</span>
                    </button>
                    <button
                      onClick={() => onContactOwner?.(property)}
                      className="p-3 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105"
                      style={{ backgroundColor: colors.accentColor, color: colors.buttonText }}
                      onMouseEnter={(e) => {
                        e.currentTarget.style.backgroundColor = colors.buttonHover;
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.backgroundColor = colors.accentColor;
                      }}
                    >
                      <MessageCircle className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Modern view more button with dynamic colors */}
        <div className="text-center mt-16">
          <button
            className="group px-12 py-4 rounded-2xl font-bold text-lg transition-all duration-300 shadow-2xl transform hover:scale-105 flex items-center space-x-3 mx-auto"
            style={{
              backgroundColor: colors.buttonBg,
              color: colors.buttonText,
              boxShadow: `0 25px 50px -12px ${colors.buttonBg}40`
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = colors.buttonHover;
              e.currentTarget.style.boxShadow = `0 25px 50px -12px ${colors.buttonBg}66`;
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = colors.buttonBg;
              e.currentTarget.style.boxShadow = `0 25px 50px -12px ${colors.buttonBg}40`;
            }}
          >
            <span>Lihat Semua Properti</span>
            <div className="rounded-full p-1 group-hover:rotate-45 transition-transform duration-300" style={{ backgroundColor: `${colors.buttonText}33` }}>
              <ChevronRight className="w-5 h-5" />
            </div>
          </button>
          <p className="mt-4 font-medium" style={{ color: colors.secondaryText }}>Temukan lebih dari 2000+ kost berkualitas lainnya</p>
        </div>
      </div>
    </section>
  );
};

export default FeaturedProperties;
