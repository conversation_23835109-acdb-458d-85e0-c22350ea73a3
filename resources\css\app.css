@import 'tailwindcss';

@plugin 'tailwindcss-animate';

@source '../views';
@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';

@custom-variant dark (&:is(.dark *));

@theme {
    --font-sans:
        'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

/* ColorHunt Light Mode Palette: https://colorhunt.co/palette/27374d526d829db2bfdde6ed */
:root {
    /* Base ColorHunt Palette */
    --color-primary: #27374D;      /* Dark navy blue */
    --color-secondary: #526D82;    /* Medium blue-gray */
    --color-accent: #9DB2BF;       /* Light blue-gray */
    --color-background: #DDE6ED;   /* Very light blue-gray */

    /* Shadcn/UI Compatible Variables - Mapped to ColorHunt */
    --background: #DDE6ED;                       /* Light blue-gray background */
    --foreground: #27374D;                       /* Dark navy blue text */
    --card: #ffffff;                             /* Pure white for cards */
    --card-foreground: #27374D;                  /* Dark navy blue text on cards */
    --popover: #ffffff;                          /* Pure white for popovers */
    --popover-foreground: #27374D;               /* Dark navy blue text */
    --primary: #27374D;                          /* Dark navy blue primary */
    --primary-foreground: #DDE6ED;               /* Light background on primary */
    --secondary: #9DB2BF;                        /* Light blue-gray secondary */
    --secondary-foreground: #27374D;             /* Dark navy blue on secondary */
    --muted: rgba(157, 178, 191, 0.3);           /* Muted accent color */
    --muted-foreground: #526D82;                 /* Medium blue-gray for muted text */
    --accent: #9DB2BF;                           /* Light blue-gray accent */
    --accent-foreground: #27374D;                /* Dark navy blue on accent */
    --destructive: #dc2626;                      /* Standard red for destructive */
    --destructive-foreground: #ffffff;           /* White text on destructive */
    --border: rgba(157, 178, 191, 0.3);          /* Light border using accent */
    --input: rgba(221, 230, 237, 0.8);           /* Light input background */
    --ring: #27374D;                             /* Primary color for focus rings */

    /* Chart Colors */
    --chart-1: #27374D;                          /* Primary navy */
    --chart-2: #526D82;                          /* Secondary blue-gray */
    --chart-3: #9DB2BF;                          /* Accent light blue */
    --chart-4: #7c9885;                          /* Complementary green */
    --chart-5: #a67c7c;                          /* Complementary warm tone */

    /* Sidebar Variables */
    --sidebar: #ffffff;                          /* White sidebar background */
    --sidebar-foreground: #27374D;               /* Dark navy text */
    --sidebar-primary: #27374D;                  /* Primary navy */
    --sidebar-primary-foreground: #DDE6ED;       /* Light text on primary */
    --sidebar-accent: #9DB2BF;                   /* Accent color */
    --sidebar-accent-foreground: #27374D;        /* Dark text on accent */
    --sidebar-border: rgba(157, 178, 191, 0.3);  /* Light border */
    --sidebar-ring: #27374D;                     /* Primary focus ring */

    /* Border Radius */
    --radius: 0.625rem;

    /* Extended ColorHunt Variables for Landing Page */
    --color-primary-50: rgba(39, 55, 77, 0.05);
    --color-primary-100: rgba(39, 55, 77, 0.1);
    --color-primary-200: rgba(39, 55, 77, 0.2);
    --color-primary-500: rgba(39, 55, 77, 0.5);
    --color-primary-700: rgba(39, 55, 77, 0.7);
    --color-primary-900: rgba(39, 55, 77, 0.9);

    --color-secondary-50: rgba(82, 109, 130, 0.05);
    --color-secondary-100: rgba(82, 109, 130, 0.1);
    --color-secondary-200: rgba(82, 109, 130, 0.2);
    --color-secondary-500: rgba(82, 109, 130, 0.5);
    --color-secondary-700: rgba(82, 109, 130, 0.7);

    --color-accent-50: rgba(157, 178, 191, 0.05);
    --color-accent-100: rgba(157, 178, 191, 0.1);
    --color-accent-200: rgba(157, 178, 191, 0.2);
    --color-accent-500: rgba(157, 178, 191, 0.5);
    --color-accent-700: rgba(157, 178, 191, 0.7);
}

/* ColorHunt Dark Mode Palette: https://colorhunt.co/palette/1e201e3c3d37697565ecdfcc */
.dark {
    /* Base ColorHunt Dark Palette */
    --color-primary: #1E201E;      /* Very dark gray-black */
    --color-secondary: #3C3D37;    /* Dark olive gray */
    --color-accent: #697565;       /* Medium olive green */
    --color-background: #ECDFCC;   /* Light cream beige */

    /* Shadcn/UI Compatible Variables - Mapped to ColorHunt Dark */
    --background: #1E201E;                       /* Very dark gray-black background */
    --foreground: #ECDFCC;                       /* Light cream text */
    --card: rgba(60, 61, 55, 0.8);               /* Dark olive card background */
    --card-foreground: #ECDFCC;                  /* Light cream text on cards */
    --popover: #3C3D37;                          /* Dark olive popover */
    --popover-foreground: #ECDFCC;               /* Light cream text */
    --primary: #ECDFCC;                          /* Light cream primary (inverted for dark) */
    --primary-foreground: #1E201E;               /* Dark background on primary */
    --secondary: #697565;                        /* Medium olive secondary */
    --secondary-foreground: #ECDFCC;             /* Light cream on secondary */
    --muted: rgba(105, 117, 101, 0.3);           /* Muted olive color */
    --muted-foreground: rgba(236, 223, 204, 0.7); /* Muted light text */
    --accent: #697565;                           /* Medium olive accent */
    --accent-foreground: #ECDFCC;                /* Light cream on accent */
    --destructive: #dc2626;                      /* Standard red for destructive */
    --destructive-foreground: #ECDFCC;           /* Light text on destructive */
    --border: rgba(105, 117, 101, 0.3);          /* Olive border */
    --input: rgba(30, 32, 30, 0.7);              /* Dark input background */
    --ring: #ECDFCC;                             /* Light cream focus rings */

    /* Chart Colors */
    --chart-1: #ECDFCC;                          /* Light cream */
    --chart-2: #697565;                          /* Medium olive */
    --chart-3: #3C3D37;                          /* Dark olive */
    --chart-4: #8a9a8d;                          /* Lighter olive variant */
    --chart-5: #d4c4a8;                          /* Warmer cream variant */

    /* Sidebar Variables */
    --sidebar: #1E201E;                          /* Dark background */
    --sidebar-foreground: #ECDFCC;               /* Light text */
    --sidebar-primary: #ECDFCC;                  /* Light primary */
    --sidebar-primary-foreground: #1E201E;       /* Dark text on primary */
    --sidebar-accent: #697565;                   /* Olive accent */
    --sidebar-accent-foreground: #ECDFCC;        /* Light text on accent */
    --sidebar-border: rgba(105, 117, 101, 0.3);  /* Olive border */
    --sidebar-ring: #ECDFCC;                     /* Light focus ring */

    /* Extended ColorHunt Dark Variables */
    --color-primary-50: rgba(30, 32, 30, 0.05);
    --color-primary-100: rgba(30, 32, 30, 0.1);
    --color-primary-200: rgba(30, 32, 30, 0.2);
    --color-primary-500: rgba(30, 32, 30, 0.5);
    --color-primary-700: rgba(30, 32, 30, 0.7);
    --color-primary-900: rgba(30, 32, 30, 0.9);

    --color-secondary-50: rgba(60, 61, 55, 0.05);
    --color-secondary-100: rgba(60, 61, 55, 0.1);
    --color-secondary-200: rgba(60, 61, 55, 0.2);
    --color-secondary-500: rgba(60, 61, 55, 0.5);
    --color-secondary-700: rgba(60, 61, 55, 0.7);

    --color-accent-50: rgba(105, 117, 101, 0.05);
    --color-accent-100: rgba(105, 117, 101, 0.1);
    --color-accent-200: rgba(105, 117, 101, 0.2);
    --color-accent-500: rgba(105, 117, 101, 0.5);
    --color-accent-700: rgba(105, 117, 101, 0.7);
}

@layer base {
    * {
        @apply border-border;
    }

    body {
        @apply bg-background text-foreground;
    }
}



/* Custom animations for modern design */
@keyframes blob {
    0% {
        transform: translate(0px, 0px) scale(1);
    }
    33% {
        transform: translate(30px, -50px) scale(1.1);
    }
    66% {
        transform: translate(-20px, 20px) scale(0.9);
    }
    100% {
        transform: translate(0px, 0px) scale(1);
    }
}

.animate-blob {
    animation: blob 7s infinite;
}

.animation-delay-2000 {
    animation-delay: 2s;
}

.animation-delay-4000 {
    animation-delay: 4s;
}
